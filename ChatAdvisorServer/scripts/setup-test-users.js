#!/usr/bin/env node

/**
 * 测试用户创建脚本
 * 用于快速创建测试用户和余额记录，支持AI聊天兼容性测试
 * 
 * 使用方法:
 * node scripts/setup-test-users.js [options]
 * 
 * 选项:
 * --env <environment>  指定环境 (development|test|production) 默认: development
 * --db <database>      指定数据库名 默认: ChatAdvisor_test
 * --reset             重置所有测试数据
 * --debug             显示详细调试信息
 */

const mongoose = require('mongoose');
const crypto = require('crypto');
const jwt = require('jsonwebtoken');

// 命令行参数解析
const args = process.argv.slice(2);
const options = {
    env: 'development',
    db: 'ChatAdvisor_test',
    reset: false,
    debug: false
};

for (let i = 0; i < args.length; i++) {
    switch (args[i]) {
        case '--env':
            options.env = args[++i];
            break;
        case '--db':
            options.db = args[++i];
            break;
        case '--reset':
            options.reset = true;
            break;
        case '--debug':
            options.debug = true;
            break;
        case '--help':
        case '-h':
            console.log(`
测试用户创建脚本

使用方法:
  node scripts/setup-test-users.js [options]

选项:
  --env <environment>  指定环境 (development|test|production) 默认: development
  --db <database>      指定数据库名 默认: ChatAdvisor_test
  --reset             重置所有测试数据
  --debug             显示详细调试信息
  --help, -h          显示此帮助信息

示例:
  node scripts/setup-test-users.js
  node scripts/setup-test-users.js --env test --db ChatAdvisor_test
  node scripts/setup-test-users.js --reset --debug
            `);
            process.exit(0);
    }
}

// 测试用户数据
const testUsers = [
    {
        _id: "6891f0c00edf7fc5c5b6cc64",
        username: "<EMAIL>",
        password: "8d969eef6ecad3c29a3a629280e686cf0c3f5d5a86aff3ca12020c923adc6c92", // hello
        email: "<EMAIL>",
        language: "zh_CN",
        allergies: [],
        medicalConditions: [],
        balance: 50,
        role: "user",
        status: "active",
        isDelete: false,
        hasPurchase: false,
        createdAt: new Date("2025-08-05T11:53:36.847Z"),
        updatedAt: new Date("2025-08-05T11:53:36.847Z")
    },
    {
        _id: "507f1f77bcf86cd799439011", // 调试用户ID
        username: "<EMAIL>",
        password: "debug_password_hash",
        email: "<EMAIL>",
        language: "zh_CN",
        allergies: [],
        medicalConditions: [],
        balance: 100,
        role: "user",
        status: "active",
        isDelete: false,
        hasPurchase: false,
        createdAt: new Date(),
        updatedAt: new Date()
    }
];

// 调试函数
function debugLog(message, data = null) {
    if (options.debug) {
        console.log(`[DEBUG] ${message}`);
        if (data) {
            console.log(JSON.stringify(data, null, 2));
        }
    }
}

// 生成JWT Token
function generateJWTToken(userId, email) {
    try {
        // 使用与服务器相同的密钥生成逻辑
        const originalSecretKey = 'dev_jwt_secret_key_for_development_only';
        const hash = crypto.createHash('sha256');
        hash.update(originalSecretKey);
        const secretKey = hash.digest('hex');

        const payload = {
            userId: userId,
            email: email,
            iat: Math.floor(Date.now() / 1000),
            exp: Math.floor(Date.now() / 1000) + (7 * 24 * 60 * 60) // 7天过期
        };

        const token = jwt.sign(payload, secretKey);
        return token;
    } catch (error) {
        console.error('生成JWT Token失败:', error.message);
        return null;
    }
}

// 主要执行函数
async function setupTestUsers() {
    try {
        console.log(`🚀 开始设置测试用户...`);
        console.log(`📊 环境: ${options.env}`);
        console.log(`💾 数据库: ${options.db}`);
        
        // 连接数据库
        const mongoUrl = `mongodb://localhost:27017/${options.db}`;
        await mongoose.connect(mongoUrl);
        console.log(`✅ 数据库连接成功: ${mongoUrl}`);

        // 定义模型
        const User = mongoose.model('User', new mongoose.Schema({}, { strict: false }));
        const UserBalance = mongoose.model('UserBalance', new mongoose.Schema({
            userId: { type: mongoose.Schema.Types.Mixed, required: true },
            balance: { type: Number, default: 50 },
            createdAt: { type: Date, default: Date.now },
            updatedAt: { type: Date, default: Date.now }
        }));

        // 重置数据（如果指定）
        if (options.reset) {
            console.log('🗑️  重置测试数据...');
            await User.deleteMany({ 
                _id: { $in: testUsers.map(u => u._id) } 
            });
            await UserBalance.deleteMany({ 
                userId: { $in: testUsers.map(u => u._id) } 
            });
            console.log('✅ 测试数据重置完成');
        }

        console.log('\n👥 创建测试用户...');
        
        for (const userData of testUsers) {
            const userId = userData._id;
            
            // 检查用户是否存在
            const existingUser = await User.findById(userId);
            if (existingUser) {
                console.log(`⚠️  用户已存在: ${userData.email} (${userId})`);
            } else {
                // 创建用户
                const newUser = new User(userData);
                await newUser.save();
                console.log(`✅ 用户创建成功: ${userData.email} (${userId})`);
            }

            // 检查用户余额是否存在
            const existingBalance = await UserBalance.findOne({ userId });
            if (existingBalance) {
                console.log(`⚠️  用户余额已存在: ${userData.email} - 余额: ${existingBalance.balance}`);
            } else {
                // 创建用户余额
                const newBalance = new UserBalance({
                    userId: userId,
                    balance: userData.balance
                });
                await newBalance.save();
                console.log(`✅ 用户余额创建成功: ${userData.email} - 余额: ${userData.balance}`);
            }

            // 生成JWT Token（仅对非调试用户）
            if (userId !== "507f1f77bcf86cd799439011") {
                const token = generateJWTToken(userId, userData.email);
                if (token) {
                    console.log(`🔑 JWT Token: ${token.substring(0, 50)}...`);
                }
            }
        }

        console.log('\n🔧 调试Token信息:');
        console.log(`🔑 调试Token: DEBUG_a7f3e9d2c8b1f4e6a9c2d5f8b3e7a1c4d6f9b2e5a8c1d4f7b0e3a6c9d2f5e8b1`);
        console.log(`👤 调试用户ID: 507f1f77bcf86cd799439011`);
        console.log(`📧 调试用户邮箱: <EMAIL>`);

        console.log('\n📋 测试用户汇总:');
        const users = await User.find({ 
            _id: { $in: testUsers.map(u => u._id) } 
        }).lean();
        
        for (const user of users) {
            const balance = await UserBalance.findOne({ userId: user._id }).lean();
            console.log(`👤 ${user.email} (${user._id}) - 余额: ${balance ? balance.balance : '未设置'}`);
        }

        await mongoose.disconnect();
        console.log('\n✅ 测试用户设置完成！');
        
    } catch (error) {
        console.error('❌ 设置失败:', error.message);
        if (options.debug) {
            console.error(error.stack);
        }
        process.exit(1);
    }
}

// 执行脚本
if (require.main === module) {
    setupTestUsers();
}

module.exports = { setupTestUsers, generateJWTToken, testUsers };
