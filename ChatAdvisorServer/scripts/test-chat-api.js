#!/usr/bin/env node

/**
 * AI聊天API测试脚本
 * 用于测试不同平台的AI聊天兼容性
 * 
 * 使用方法:
 * node scripts/test-chat-api.js [options]
 */

const axios = require('axios');
const { generateJWTToken, testUsers } = require('./setup-test-users');

// 命令行参数解析
const args = process.argv.slice(2);
const options = {
    host: 'http://localhost:33001',
    token: null,
    useDebugToken: false,
    model: null,
    stream: false,
    message: '你好，请简单介绍一下你自己',
    chatId: `test-chat-${Date.now()}`,
    verbose: false
};

for (let i = 0; i < args.length; i++) {
    switch (args[i]) {
        case '--host':
            options.host = args[++i];
            break;
        case '--token':
            options.token = args[++i];
            break;
        case '--debug-token':
            options.useDebugToken = true;
            break;
        case '--model':
            options.model = args[++i];
            break;
        case '--stream':
            options.stream = true;
            break;
        case '--message':
            options.message = args[++i];
            break;
        case '--chat-id':
            options.chatId = args[++i];
            break;
        case '--verbose':
        case '-v':
            options.verbose = true;
            break;
        case '--help':
        case '-h':
            console.log(`
AI聊天API测试脚本

使用方法:
  node scripts/test-chat-api.js [options]

选项:
  --host <url>         API服务器地址 默认: http://localhost:33001
  --token <token>      JWT认证Token
  --debug-token        使用调试Token
  --model <model>      指定AI模型
  --stream             启用流式响应
  --message <text>     测试消息内容
  --chat-id <id>       聊天会话ID
  --verbose, -v        显示详细信息
  --help, -h           显示此帮助信息

示例:
  node scripts/test-chat-api.js --debug-token
  node scripts/test-chat-api.js --token "eyJ..." --model "gpt-3.5-turbo"
  node scripts/test-chat-api.js --debug-token --stream --verbose
            `);
            process.exit(0);
    }
}

// 获取认证Token
function getAuthToken() {
    if (options.useDebugToken) {
        return 'DEBUG_a7f3e9d2c8b1f4e6a9c2d5f8b3e7a1c4d6f9b2e5a8c1d4f7b0e3a6c9d2f5e8b1';
    }
    
    if (options.token) {
        return options.token;
    }
    
    // 生成测试用户的JWT Token
    const testUser = testUsers[0]; // 使用第一个测试用户
    return generateJWTToken(testUser._id, testUser.email);
}

// 测试用户余额
async function testUserBalance(token) {
    try {
        console.log('📊 测试用户余额查询...');
        
        const response = await axios.get(`${options.host}/api/auth/userBalance`, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        
        console.log('✅ 用户余额查询成功');
        if (options.verbose) {
            console.log('响应数据:', JSON.stringify(response.data, null, 2));
        } else {
            console.log(`💰 当前余额: ${response.data.data?.balance || '未知'}`);
        }
        
        return response.data;
    } catch (error) {
        console.error('❌ 用户余额查询失败:', error.response?.data || error.message);
        return null;
    }
}

// 测试聊天API
async function testChatAPI(token) {
    try {
        console.log('💬 测试聊天API...');
        console.log(`📝 消息: ${options.message}`);
        console.log(`🆔 会话ID: ${options.chatId}`);
        
        const requestData = {
            messages: [
                {
                    role: 'user',
                    content: options.message
                }
            ],
            stream: options.stream
        };
        
        if (options.model) {
            requestData.model = options.model;
            console.log(`🤖 指定模型: ${options.model}`);
        }
        
        const headers = {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
            'chatid': options.chatId
        };
        
        if (options.verbose) {
            console.log('请求头:', headers);
            console.log('请求数据:', JSON.stringify(requestData, null, 2));
        }
        
        const startTime = Date.now();
        
        const response = await axios.post(`${options.host}/api/chat`, requestData, {
            headers,
            timeout: 30000
        });
        
        const endTime = Date.now();
        const responseTime = (endTime - startTime) / 1000;
        
        console.log(`✅ 聊天请求成功 (${responseTime.toFixed(2)}s)`);
        console.log(`📊 状态码: ${response.status}`);
        
        if (options.verbose) {
            console.log('完整响应:', response.data);
        } else {
            // 尝试解析响应内容
            const responseText = response.data;
            if (typeof responseText === 'string') {
                // 处理流式响应
                const lines = responseText.split('\n').filter(line => line.trim());
                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        try {
                            const data = JSON.parse(line.substring(6));
                            if (data.choices && data.choices[0] && data.choices[0].delta && data.choices[0].delta.content) {
                                console.log('🤖 AI回复:', data.choices[0].delta.content);
                            }
                        } catch (e) {
                            // 忽略解析错误
                        }
                    }
                }
            } else {
                console.log('🤖 AI回复:', responseText);
            }
        }
        
        return response.data;
    } catch (error) {
        console.error('❌ 聊天请求失败:', error.response?.data || error.message);
        if (error.response?.status) {
            console.error(`📊 状态码: ${error.response.status}`);
        }
        return null;
    }
}

// 测试健康检查
async function testHealthCheck() {
    try {
        console.log('🏥 测试健康检查...');
        
        const response = await axios.get(`${options.host}/health`, {
            timeout: 5000
        });
        
        console.log('✅ 服务器健康检查通过');
        if (options.verbose) {
            console.log('健康状态:', JSON.stringify(response.data, null, 2));
        }
        
        return true;
    } catch (error) {
        console.error('❌ 服务器健康检查失败:', error.message);
        return false;
    }
}

// 主测试函数
async function runTests() {
    try {
        console.log('🚀 开始AI聊天兼容性测试...');
        console.log(`🌐 服务器: ${options.host}`);
        
        // 1. 健康检查
        const isHealthy = await testHealthCheck();
        if (!isHealthy) {
            console.error('❌ 服务器不可用，测试终止');
            process.exit(1);
        }
        
        // 2. 获取认证Token
        const token = getAuthToken();
        if (!token) {
            console.error('❌ 无法获取认证Token，测试终止');
            process.exit(1);
        }
        
        console.log(`🔑 使用Token: ${options.useDebugToken ? '调试Token' : 'JWT Token'}`);
        if (options.verbose) {
            console.log(`Token: ${token.substring(0, 50)}...`);
        }
        
        // 3. 测试用户余额
        await testUserBalance(token);
        
        console.log(''); // 空行分隔
        
        // 4. 测试聊天API
        await testChatAPI(token);
        
        console.log('\n✅ 测试完成！');
        
    } catch (error) {
        console.error('❌ 测试过程中发生错误:', error.message);
        if (options.verbose) {
            console.error(error.stack);
        }
        process.exit(1);
    }
}

// 执行测试
if (require.main === module) {
    runTests();
}

module.exports = { testUserBalance, testChatAPI, testHealthCheck };
