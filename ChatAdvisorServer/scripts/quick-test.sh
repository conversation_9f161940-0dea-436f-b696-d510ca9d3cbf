#!/bin/bash

# AI聊天快速测试脚本
# 一键设置测试用户并进行聊天测试

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
HOST="http://localhost:33001"
DEBUG_TOKEN="DEBUG_a7f3e9d2c8b1f4e6a9c2d5f8b3e7a1c4d6f9b2e5a8c1d4f7b0e3a6c9d2f5e8b1"
TEST_MESSAGE="你好，请简单介绍一下你自己"

# 函数：打印带颜色的消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_header() {
    echo -e "${BLUE}🚀 $1${NC}"
    echo "=================================="
}

# 函数：检查依赖
check_dependencies() {
    print_header "检查依赖"
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        print_error "Node.js 未安装"
        exit 1
    fi
    print_success "Node.js: $(node --version)"
    
    # 检查MongoDB
    if ! command -v mongosh &> /dev/null && ! command -v mongo &> /dev/null; then
        print_warning "MongoDB客户端未找到，将跳过数据库检查"
    else
        print_success "MongoDB客户端已安装"
    fi
    
    # 检查curl
    if ! command -v curl &> /dev/null; then
        print_error "curl 未安装"
        exit 1
    fi
    print_success "curl 已安装"
    
    echo ""
}

# 函数：检查服务器状态
check_server() {
    print_header "检查服务器状态"
    
    if curl -s "$HOST/health" > /dev/null 2>&1; then
        print_success "服务器运行正常: $HOST"
    else
        print_error "服务器无法访问: $HOST"
        print_info "请确保服务器已启动: yarn dev"
        exit 1
    fi
    
    echo ""
}

# 函数：设置测试用户
setup_users() {
    print_header "设置测试用户"
    
    if [ -f "scripts/setup-test-users.js" ]; then
        print_info "创建测试用户..."
        if node scripts/setup-test-users.js --debug; then
            print_success "测试用户创建完成"
        else
            print_error "测试用户创建失败"
            exit 1
        fi
    else
        print_error "找不到 scripts/setup-test-users.js"
        exit 1
    fi
    
    echo ""
}

# 函数：测试用户余额
test_balance() {
    print_header "测试用户余额"
    
    print_info "查询调试用户余额..."
    
    RESPONSE=$(curl -s -X GET "$HOST/api/auth/userBalance" \
        -H "Authorization: Bearer $DEBUG_TOKEN" \
        -H "Content-Type: application/json")
    
    if echo "$RESPONSE" | grep -q '"code":200'; then
        BALANCE=$(echo "$RESPONSE" | grep -o '"balance":[0-9]*' | cut -d':' -f2)
        print_success "用户余额查询成功: $BALANCE"
    else
        print_error "用户余额查询失败"
        print_info "响应: $RESPONSE"
        return 1
    fi
    
    echo ""
}

# 函数：测试聊天API
test_chat() {
    print_header "测试聊天API"
    
    print_info "发送测试消息: $TEST_MESSAGE"
    
    CHAT_ID="quick-test-$(date +%s)"
    
    RESPONSE=$(curl -s -X POST "$HOST/api/chat" \
        -H "Authorization: Bearer $DEBUG_TOKEN" \
        -H "Content-Type: application/json" \
        -H "chatid: $CHAT_ID" \
        -w "\n%{http_code}" \
        -d "{
            \"messages\": [
                {
                    \"role\": \"user\",
                    \"content\": \"$TEST_MESSAGE\"
                }
            ],
            \"stream\": false
        }")
    
    # 分离响应体和状态码
    HTTP_CODE=$(echo "$RESPONSE" | tail -n1)
    RESPONSE_BODY=$(echo "$RESPONSE" | head -n -1)
    
    if [ "$HTTP_CODE" = "200" ]; then
        print_success "聊天请求成功 (状态码: $HTTP_CODE)"
        
        # 尝试解析响应内容
        if echo "$RESPONSE_BODY" | grep -q "data:"; then
            print_info "收到流式响应数据"
            # 提取可能的AI回复内容
            AI_CONTENT=$(echo "$RESPONSE_BODY" | grep -o '"content":"[^"]*"' | head -1 | cut -d'"' -f4)
            if [ -n "$AI_CONTENT" ]; then
                print_success "AI回复: $AI_CONTENT"
            else
                print_warning "响应格式可能为空或异常"
            fi
        else
            print_info "响应: $RESPONSE_BODY"
        fi
    else
        print_error "聊天请求失败 (状态码: $HTTP_CODE)"
        print_info "响应: $RESPONSE_BODY"
        return 1
    fi
    
    echo ""
}

# 函数：运行完整测试
run_full_test() {
    print_header "AI聊天兼容性测试"
    echo "开始时间: $(date)"
    echo ""
    
    check_dependencies
    check_server
    setup_users
    
    if test_balance && test_chat; then
        print_success "🎉 所有测试通过！"
        echo ""
        print_info "测试总结:"
        echo "  - 服务器状态: 正常"
        echo "  - 用户认证: 成功"
        echo "  - 余额查询: 成功"
        echo "  - 聊天API: 成功"
        echo ""
        print_info "你现在可以使用以下信息进行更多测试:"
        echo "  - 调试Token: $DEBUG_TOKEN"
        echo "  - 测试用户ID: 507f1f77bcf86cd799439011"
        echo "  - API地址: $HOST"
    else
        print_error "测试失败，请检查上述错误信息"
        exit 1
    fi
}

# 函数：显示帮助信息
show_help() {
    echo "AI聊天快速测试脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --setup-only    仅设置测试用户"
    echo "  --test-only     仅运行测试（跳过用户设置）"
    echo "  --balance-only  仅测试用户余额"
    echo "  --chat-only     仅测试聊天API"
    echo "  --host <url>    指定服务器地址 (默认: $HOST)"
    echo "  --message <msg> 指定测试消息"
    echo "  --help          显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                                    # 运行完整测试"
    echo "  $0 --setup-only                      # 仅设置用户"
    echo "  $0 --test-only                       # 仅运行测试"
    echo "  $0 --host http://localhost:3001      # 指定服务器"
    echo "  $0 --message \"Hello AI\"              # 自定义测试消息"
}

# 主程序
main() {
    # 解析命令行参数
    SETUP_ONLY=false
    TEST_ONLY=false
    BALANCE_ONLY=false
    CHAT_ONLY=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --setup-only)
                SETUP_ONLY=true
                shift
                ;;
            --test-only)
                TEST_ONLY=true
                shift
                ;;
            --balance-only)
                BALANCE_ONLY=true
                shift
                ;;
            --chat-only)
                CHAT_ONLY=true
                shift
                ;;
            --host)
                HOST="$2"
                shift 2
                ;;
            --message)
                TEST_MESSAGE="$2"
                shift 2
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                print_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 执行相应的操作
    if [ "$SETUP_ONLY" = true ]; then
        check_dependencies
        setup_users
    elif [ "$TEST_ONLY" = true ]; then
        check_dependencies
        check_server
        test_balance
        test_chat
    elif [ "$BALANCE_ONLY" = true ]; then
        check_dependencies
        check_server
        test_balance
    elif [ "$CHAT_ONLY" = true ]; then
        check_dependencies
        check_server
        test_chat
    else
        run_full_test
    fi
}

# 检查是否在正确的目录中
if [ ! -f "package.json" ] || [ ! -d "src" ]; then
    print_error "请在ChatAdvisorServer项目根目录中运行此脚本"
    exit 1
fi

# 运行主程序
main "$@"
