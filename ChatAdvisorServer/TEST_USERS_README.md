# AI聊天测试用户快速设置指南

本文档提供了快速创建和使用测试用户进行AI聊天兼容性测试的完整指南。

## 📋 目录

- [快速开始](#快速开始)
- [脚本说明](#脚本说明)
- [测试用户信息](#测试用户信息)
- [使用示例](#使用示例)
- [故障排除](#故障排除)
- [API测试](#api测试)

## 🚀 快速开始

### 1. 创建测试用户

```bash
# 基础创建（使用默认设置）
node scripts/setup-test-users.js

# 指定环境和数据库
node scripts/setup-test-users.js --env development --db ChatAdvisor_test

# 重置并重新创建所有测试数据
node scripts/setup-test-users.js --reset --debug
```

### 2. 测试AI聊天

```bash
# 使用调试Token进行基础测试
node scripts/test-chat-api.js --debug-token

# 使用JWT Token测试
node scripts/test-chat-api.js --token "your_jwt_token_here"

# 详细测试（显示完整响应）
node scripts/test-chat-api.js --debug-token --verbose

# 测试流式响应
node scripts/test-chat-api.js --debug-token --stream
```

### 3. 手动curl测试

```bash
# 测试用户余额
curl -X GET "http://localhost:33001/api/auth/userBalance" \
  -H "Authorization: Bearer DEBUG_a7f3e9d2c8b1f4e6a9c2d5f8b3e7a1c4d6f9b2e5a8c1d4f7b0e3a6c9d2f5e8b1" \
  -H "Content-Type: application/json"

# 测试聊天API
curl -X POST "http://localhost:33001/api/chat" \
  -H "Authorization: Bearer DEBUG_a7f3e9d2c8b1f4e6a9c2d5f8b3e7a1c4d6f9b2e5a8c1d4f7b0e3a6c9d2f5e8b1" \
  -H "Content-Type: application/json" \
  -H "chatid: test-chat-001" \
  -d '{
    "messages": [
      {
        "role": "user",
        "content": "你好，请简单介绍一下你自己"
      }
    ],
    "stream": false
  }'
```

## 📝 脚本说明

### setup-test-users.js

**功能**: 创建测试用户和余额记录

**选项**:
- `--env <environment>`: 指定环境 (development|test|production)
- `--db <database>`: 指定数据库名
- `--reset`: 重置所有测试数据
- `--debug`: 显示详细调试信息

### test-chat-api.js

**功能**: 测试AI聊天API的兼容性

**选项**:
- `--host <url>`: API服务器地址
- `--token <token>`: JWT认证Token
- `--debug-token`: 使用调试Token
- `--model <model>`: 指定AI模型
- `--stream`: 启用流式响应
- `--message <text>`: 测试消息内容
- `--chat-id <id>`: 聊天会话ID
- `--verbose`: 显示详细信息

## 👥 测试用户信息

### 用户1: 标准测试用户
- **用户ID**: `6891f0c00edf7fc5c5b6cc64`
- **邮箱**: `<EMAIL>`
- **密码**: `hello` (哈希值: `8d969eef6ecad3c29a3a629280e686cf0c3f5d5a86aff3ca12020c923adc6c92`)
- **初始余额**: 50
- **用途**: 标准JWT Token测试

### 用户2: 调试用户
- **用户ID**: `507f1f77bcf86cd799439011`
- **邮箱**: `<EMAIL>`
- **初始余额**: 100
- **调试Token**: `DEBUG_a7f3e9d2c8b1f4e6a9c2d5f8b3e7a1c4d6f9b2e5a8c1d4f7b0e3a6c9d2f5e8b1`
- **用途**: 开发环境快速测试

## 💡 使用示例

### 场景1: 新环境快速测试

```bash
# 1. 创建测试用户
node scripts/setup-test-users.js --reset

# 2. 测试服务器连通性
node scripts/test-chat-api.js --debug-token --message "Hello, World!"

# 3. 测试不同模型
node scripts/test-chat-api.js --debug-token --model "gpt-3.5-turbo"
```

### 场景2: 生产环境兼容性测试

```bash
# 1. 创建测试用户（不重置现有数据）
node scripts/setup-test-users.js --env production --db ChatAdvisor_prod

# 2. 使用JWT Token测试
JWT_TOKEN=$(node -e "console.log(require('./scripts/setup-test-users').generateJWTToken('6891f0c00edf7fc5c5b6cc64', '<EMAIL>'))")
node scripts/test-chat-api.js --token "$JWT_TOKEN" --host "https://your-api-server.com"
```

### 场景3: 流式响应测试

```bash
# 测试流式响应
node scripts/test-chat-api.js --debug-token --stream --verbose --message "请写一首关于春天的诗"
```

## 🔧 故障排除

### 问题1: 数据库连接失败
```bash
# 检查MongoDB是否运行
brew services list | grep mongodb
# 或
systemctl status mongod

# 启动MongoDB
brew services start mongodb-community
# 或
systemctl start mongod
```

### 问题2: 调试Token无效
确保环境变量正确设置：
```bash
# 检查.env.development文件
cat .env.development | grep DEBUG_TOKEN

# 重启服务器以加载环境变量
yarn dev
```

### 问题3: 用户余额查询失败
```bash
# 检查数据库中的余额记录
mongosh ChatAdvisor_test --eval "db.userbalances.find({userId: '507f1f77bcf86cd799439011'}).pretty()"

# 重新创建余额记录
node scripts/setup-test-users.js --reset
```

### 问题4: AI模型配额用尽
```bash
# 检查可用模型
mongosh ChatAdvisor_test --eval "db.aiservicemodels.find({isActive: true}).pretty()"

# 使用不同模型测试
node scripts/test-chat-api.js --debug-token --model "gpt-3.5-turbo"
```

## 🧪 API测试

### 健康检查
```bash
curl -s http://localhost:33001/health | jq
```

### 用户余额查询
```bash
curl -X GET "http://localhost:33001/api/auth/userBalance" \
  -H "Authorization: Bearer DEBUG_a7f3e9d2c8b1f4e6a9c2d5f8b3e7a1c4d6f9b2e5a8c1d4f7b0e3a6c9d2f5e8b1" \
  -H "Content-Type: application/json" | jq
```

### 聊天API测试
```bash
curl -X POST "http://localhost:33001/api/chat" \
  -H "Authorization: Bearer DEBUG_a7f3e9d2c8b1f4e6a9c2d5f8b3e7a1c4d6f9b2e5a8c1d4f7b0e3a6c9d2f5e8b1" \
  -H "Content-Type: application/json" \
  -H "chatid: test-$(date +%s)" \
  -d '{
    "messages": [{"role": "user", "content": "你好"}],
    "stream": false
  }'
```

## 📚 相关文档

- [API文档](./API_DOCS.md)
- [环境配置](./ENVIRONMENT_SETUP.md)
- [部署指南](./DEPLOYMENT.md)

## 🤝 贡献

如果你发现问题或有改进建议，请：
1. 创建Issue描述问题
2. 提交Pull Request
3. 更新相关文档

---

**注意**: 调试Token仅在开发和测试环境中有效，生产环境会自动禁用。
